{{--
  RajaPicker Component with Form Submission Prevention

  Features:
  - Prevents all form submissions, button clicks, link clicks, and input interactions when picker modal is open
  - Only allows the "Pilih" (Select) button to function when picker is active
  - Provides visual feedback with form overlay and notification
  - Automatically re-enables form functionality when picker is closed
  - Supports ESC key to close picker
  - Cleans up all event handlers on component destruction
--}}

@php
  $id = $getId();
  $statePath = $getStatePath();
  $state = $getState();
  $isMultiple = $isMultiple();
  $previewSize = $getPreviewSize();
  $placeholder = $getPlaceholder();
  $collection = $getCollection();
  $maxFileSize = $getMaxFileSize();
  $acceptedTypes = $getAcceptedFileTypesString();

  // Get current media
  $currentMedia = null;
  if ($state) {
      if ($isMultiple && is_array($state)) {
          $currentMedia = $getMediaByIds($state);
      } else {
          $currentMedia = $getMediaById($state);
      }
  }
@endphp

<x-dynamic-component :component="$getFieldWrapperView()" :field="$field">
  <div class="raja-picker-container" x-data="rajaPicker({
      statePath: '{{ $statePath }}',
      isMultiple: {{ $isMultiple ? 'true' : 'false' }},
      collection: '{{ $collection }}',
      maxFileSize: {{ $maxFileSize }},
      acceptedTypes: '{{ $acceptedTypes }}',
      enablePicker: {{ $isPickerEnabled() ? 'true' : 'false' }},
      enableUploader: {{ $isUploaderEnabled() ? 'true' : 'false' }},
      previewSize: {{ $previewSize }},
      byUser: {{ $isByUser() ? 'true' : 'false' }},
      byUserId: {{ $getByUserId() ?? 'null' }},
      convertWebp: {{ $shouldConvertWebp() ? 'true' : 'false' }},
      currentValue: @js($state)
  })">
    <!-- Hidden input untuk menyimpan nilai -->
    <input
      {{ $applyStateBindingModifiers('wire:model') }}="{{ $getStatePath() }}"
      name="{{ $statePath }}" type="hidden" x-model="value" />

    <!-- Preview Area -->
    <div class="raja-picker-preview mb-4" x-show="hasValue">
      <template x-if="!isMultiple && selectedMedia">
        <div class="single-preview">
          <div class="relative inline-block">
            <img :alt="selectedMedia.name" :src="addStoragePrefix(selectedMedia.thumbnail_url ?? selectedMedia.url)"
              :style="`width: ${previewSize}px; height: ${previewSize}px;`"
              class="rounded-lg border border-gray-300 object-cover" />
            <button @click="removeMedia()"
              class="absolute -right-2 -top-2 flex h-6 w-6 items-center justify-center rounded-full bg-red-500 text-xs text-white transition-colors hover:bg-red-600"
              type="button">
              ×
            </button>
          </div>
          <div class="mt-2 text-sm text-gray-600"
            x-show="{{ $shouldShowFileName() ? 'true' : 'false' }}">
            <div class="font-medium" x-text="selectedMedia.name"></div>
            <div class="text-gray-500" x-show="{{ $shouldShowFileSize() ? 'true' : 'false' }}"
              x-text="formatFileSize(selectedMedia.size)"></div>
          </div>
        </div>
      </template>

      <template x-if="isMultiple && selectedMediaList.length > 0">
        <div class="multiple-preview grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
          <template :key="media.id" x-for="(media, index) in selectedMediaList">
            <div class="relative">
              <img :alt="media.name" :src="addStoragePrefix(media.thumbnail_url ?? media.url)"
                :style="`height: ${previewSize}px;`"
                class="w-full rounded-lg border border-gray-300 object-cover" />
              <button @click="removeMediaAtIndex(index)"
                class="absolute -right-2 -top-2 flex h-6 w-6 items-center justify-center rounded-full bg-red-500 text-xs text-white transition-colors hover:bg-red-600"
                type="button">
                ×
              </button>
              <div class="mt-1 text-xs text-gray-600"
                x-show="{{ $shouldShowFileName() ? 'true' : 'false' }}">
                <div class="truncate font-medium" x-text="media.name"></div>
                <div class="text-gray-500" x-show="{{ $shouldShowFileSize() ? 'true' : 'false' }}"
                  x-text="formatFileSize(media.size)"></div>
              </div>
            </div>
          </template>
        </div>
      </template>
    </div>

    <!-- Action Buttons -->
    <div class="raja-picker-actions flex flex-wrap gap-2">
      <!-- Picker Button dengan indikator loading -->
      <button @click="openPicker()"
        :disabled="isOpeningGallery"
        class="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-1 text-xs font-semibold uppercase tracking-widest text-white ring-blue-300 transition duration-150 ease-in-out hover:bg-blue-700 focus:border-blue-900 focus:outline-none focus:ring active:bg-blue-900 disabled:opacity-25"
        type="button" x-show="enablePicker">
        <span x-show="!isOpeningGallery" class="flex items-center">
          <x-phosphor-images-duotone class="h-4 w-4 mr-2" />
          Galeri
        </span>
        <span x-show="isOpeningGallery" class="flex items-center">
          <svg class="mr-2 h-4 w-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
          </svg>
          Memuat...
        </span>
      </button>

      <!-- Upload Button dengan indikator loading -->
      <button @click="$refs.fileInput.click()"
        :disabled="isUploading"
        class="inline-flex items-center rounded-md border border-transparent bg-green-600 px-4 py-1 text-xs font-semibold uppercase tracking-widest text-white ring-green-300 transition duration-150 ease-in-out hover:bg-green-700 focus:border-green-900 focus:outline-none focus:ring active:bg-green-900 disabled:opacity-25"
        type="button" x-show="enableUploader">
        <span x-show="!isUploading" class="flex items-center">
          <x-phosphor-upload-simple-duotone class="h-4 w-4 mr-2" />
          Upload
        </span>
        <span x-show="isUploading" class="flex items-center">
          <svg class="mr-2 h-4 w-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
          </svg>
          Memproses...
        </span>
      </button>

      <!-- Clear Button -->
      {{-- <button @click="clearSelection()"
        class="inline-flex items-center rounded-md border border-transparent bg-red-600 px-4 py-1 text-xs font-semibold uppercase tracking-widest text-white ring-red-300 transition duration-150 ease-in-out hover:bg-red-700 focus:border-red-900 focus:outline-none focus:ring active:bg-red-900 disabled:opacity-25"
        type="button" x-show="hasValue">
      <x-phosphor-x-circle-fill class="h-4 w-4 mr-2" />
        Hapus
      </button> --}}
    </div>

    <!-- Placeholder when no value -->
    <div
      class="raja-picker-placeholder mt-4 rounded-lg border-2 border-dashed border-gray-300 p-8 text-center text-gray-500"
      x-show="!hasValue">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor"
        viewBox="0 0 48 48">
        <path
          d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
          stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
      </svg>
      <p class="mt-2" x-text="'{{ $placeholder }}'"></p>
    </div>

    <!-- Debug info (hanya tampil di lingkungan local) -->
    @if (app()->environment('local'))
      <div class="mt-2 rounded bg-gray-100 p-2 text-xs text-gray-500">
        <details>
          <summary>Debug Info RajaPicker</summary>
          <div class="mt-2 space-y-1">
            <p><strong>State Path:</strong> {{ $statePath }}</p>
            <p><strong>Current State:</strong> <code x-text="JSON.stringify(value)"></code></p>
            <p><strong>Is Multiple:</strong> <span x-text="isMultiple"></span></p>
            <p><strong>Has Value:</strong> <span x-text="hasValue"></span></p>
            <p><strong>Collection:</strong> {{ $collection }}</p>
            <p><strong>By User:</strong> <span x-text="byUser"></span></p>
          </div>
        </details>
      </div>
    @endif

    <!-- Hidden file input for upload -->
    <input :accept="acceptedTypes" :multiple="isMultiple" @change="handleFileUpload($event)"
      class="hidden" type="file" x-ref="fileInput" />

   

    <!-- Media Picker Modal dengan styling Filament -->
    <div
      class="fixed inset-0 z-50 overflow-y-auto" style="display: none;" x-show="showPicker"
      x-transition:enter-end="opacity-100" x-transition:enter-start="opacity-0"
      x-transition:enter="ease-out duration-300" x-transition:leave-end="opacity-0"
      x-transition:leave-start="opacity-100" x-transition:leave="ease-in duration-200">
      <!-- Backdrop -->
      <div @click="closePicker()" class="fixed inset-0 bg-gray-500/75 transition-opacity"></div>

      <!-- Slide-over Panel -->
      <div class="fixed inset-y-0 right-0 flex max-w-full pl-10">
        <div
          class="w-screen max-w-5xl" x-show="showPicker" x-transition:enter-end="translate-x-0"
          x-transition:enter-start="translate-x-full"
          x-transition:enter="transform transition ease-in-out duration-500"
          x-transition:leave-end="translate-x-full" x-transition:leave-start="translate-x-0"
          x-transition:leave="transform transition ease-in-out duration-500">
          <div class="flex h-full flex-col overflow-hidden bg-white shadow-xl">
            <!-- Header -->
            <div class="border-b border-gray-200 bg-white px-6 py-4">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">
                  Pilih Gambar dari Galeri
                </h3>
                <button @click="closePicker()"
                  class="text-gray-400 transition-colors hover:text-gray-600">
                  <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round"
                      stroke-width="2"></path>
                  </svg>
                </button>
              </div>

              <!-- Collection Filter -->
              <div class="mt-4">
                <label class="mb-2 block text-sm font-medium text-gray-700">Filter
                  Collection:</label>
                <select
                  @change="loadMediaWithFilter()"
                  class="block w-full max-w-xs rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  x-model="selectedCollection">
                  <option value="all">Semua Collection</option>
                  <template :key="collection" x-for="collection in availableCollections">
                    <option :value="collection" x-text="collection"></option>
                  </template>
                </select>
              </div>
            </div>

            <!-- Content Area -->
            <div class="flex flex-1 overflow-hidden">
              <!-- Kiri: Grid Gambar (60%) -->
              <div class="flex w-3/5 flex-col border-r border-gray-200">
                <div class="flex-1 overflow-hidden">
                  <!-- Loading state -->
                  <div class="flex h-full items-center justify-center" x-show="loadingMedia">
                    <div class="text-center">
                      <div
                        class="inline-block h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600">
                      </div>
                      <p class="mt-2 text-gray-600">Memuat gambar...</p>
                    </div>
                  </div>

                  <!-- Media grid -->
                  <div class="h-full overflow-y-auto p-4" x-show="!loadingMedia">
                    <div class="grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-4">
                      <template :key="media.id" x-for="media in availableMedia">
                        <div
                          :class="{ 'ring-2 ring-blue-500': isMediaSelected(media.id) }"
                          @click="selectMediaFromPicker(media); previewMedia = media"
                          class="group relative cursor-pointer transition-opacity hover:opacity-75">
                          <div class="relative">
                            <img
                              :alt="media.name" :src="addStoragePrefix(media.thumbnail_url ?? media.url)"
                              class="max-h-18 w-full rounded-lg border border-gray-200 object-cover" style="height:150px;"/>
                            <div
                              class="absolute inset-0 rounded-lg bg-black\/0 hover:bg-black\/20 group-hover:bg-black\/20 transition-all">
                            </div>
         
                            <div class="absolute right-1 top-1"
                              x-show="isMediaSelected(media.id)">
                              <div
                                class="flex h-5 w-5 items-center justify-center rounded-full bg-blue-500 text-xs text-white">
                                ✓
                              </div>
                            </div>
                          </div>

                          <!-- File info -->
                          <div class="mt-1 space-y-1 text-xs text-gray-600">
                            <!-- File name (limited to 15 chars) -->
                            <div :title="media.file_name" class="truncate font-medium">
                              <span
                                x-text="media.file_name.length > 15 ? media.file_name.substring(0, 15) + '...' : media.file_name"></span>
                            </div>

                            <!-- Dimensions and file size -->
                            <div class="space-y-0.5 text-gray-500">
                              <div x-show="media.width && media.height">
                                <span x-text="media.width + ' × ' + media.height"></span>
                              </div>
                              <div x-show="media.size">
                                <span x-text="formatFileSize(media.size)"></span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>

                    <!-- Empty state -->
                    <div class="flex h-full items-center justify-center text-gray-500"
                      x-show="!loadingMedia && availableMedia.length === 0">
                      <div class="text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none"
                          stroke="currentColor" viewBox="0 0 48 48">
                          <path
                            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                            stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
                        </svg>
                        <p class="mt-2">Tidak ada gambar tersedia di galeri</p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Pagination di bagian bawah grid -->
                <div class="border-t border-gray-200 bg-gray-50 px-4 py-3"
                  x-show="!loadingMedia && pagination && pagination.last_page > 1">
                  <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                      Menampilkan
                      <span class="font-medium" x-text="pagination?.from || 0"></span>
                      sampai
                      <span class="font-medium" x-text="pagination?.to || 0"></span>
                      dari
                      <span class="font-medium" x-text="pagination?.total || 0"></span>
                      hasil
                    </div>
                    <div class="flex space-x-1">
                      <button
                        :class="(!pagination || pagination.current_page <= 1) ?
                        'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'"
                        :disabled="!pagination || pagination.current_page <= 1"
                        @click="loadPage((pagination?.current_page || 1) - 1)"
                        class="rounded border bg-white px-3 py-1 text-sm">
                        ‹ Prev
                      </button>

                      <template :key="page" x-for="page in getPageNumbers()">
                        <button
                          :class="page === (pagination?.current_page || 1) ? 'bg-blue-600 text-white' :
                              'bg-white hover:bg-gray-100'"
                          @click="loadPage(page)" class="rounded border px-3 py-1 text-sm"
                          x-text="page"></button>
                      </template>

                      <button
                        :class="(!pagination || pagination.current_page >= pagination.last_page) ?
                        'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'"
                        :disabled="!pagination || pagination.current_page >= pagination.last_page"
                        @click="loadPage((pagination?.current_page || 1) + 1)"
                        class="rounded border bg-white px-3 py-1 text-sm">
                        Next ›
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Kanan: Preview Area (40%) -->
              <div class="flex w-2/5 flex-col">
                <div class="border-b border-gray-200 p-4">
                  <h4 class="text-md font-medium text-gray-900">Preview</h4>
                </div>

                <div class="flex-1 overflow-y-auto p-4">
                  <!-- Preview gambar yang dipilih -->
                  <div class="space-y-4" x-show="previewMedia">
                    <!-- Gambar preview -->
                    <div class="text-center">
                      <img
                        :alt="previewMedia?.name" :src="addStoragePrefix(previewMedia?.url)"
                        class="mx-auto max-h-64 max-w-full rounded-lg border border-gray-200 object-contain" style="height:250px;" />
                    </div>

                    <!-- Detail informasi -->
                    <div class="space-y-3 text-sm">
                      <div>
                        <label
                          class="block text-xs font-medium uppercase tracking-wide text-gray-500">Nama
                          File</label>
                        <p class="mt-1 text-gray-900" x-text="previewMedia?.file_name"></p>
                      </div>

                      <div>
                        <label
                          class="block text-xs font-medium uppercase tracking-wide text-gray-500">Collection</label>
                        <p class="mt-1 text-gray-900" x-text="previewMedia?.collection_name"></p>
                      </div>

                      <div class="grid grid-cols-2 gap-4">
                        <div x-show="previewMedia?.width && previewMedia?.height">
                          <label
                            class="block text-xs font-medium uppercase tracking-wide text-gray-500">Dimensi</label>
                          <p class="mt-1 text-gray-900"
                            x-text="previewMedia?.width + ' × ' + previewMedia?.height + ' px'">
                          </p>
                        </div>

                        <div x-show="previewMedia?.size">
                          <label
                            class="block text-xs font-medium uppercase tracking-wide text-gray-500">Ukuran</label>
                          <p class="mt-1 text-gray-900"
                            x-text="formatFileSize(previewMedia?.size)"></p>
                        </div>
                      </div>

                      <div>
                        <label
                          class="block text-xs font-medium uppercase tracking-wide text-gray-500">Tipe
                          File</label>
                        <p class="mt-1 text-gray-900" x-text="previewMedia?.mime_type"></p>
                      </div>

                      <div>
                        <label
                          class="block text-xs font-medium uppercase tracking-wide text-gray-500">URL</label>
                        <p class="mt-1 break-all text-xs text-gray-900"
                          x-text="addStoragePrefix(previewMedia?.url)"></p>
                      </div>
                    </div>
                  </div>

                  <!-- Empty state untuk preview -->
                  <div class="flex h-full items-center justify-center text-gray-500"
                    x-show="!previewMedia">
                    <div class="text-center">
                      <svg class="mx-auto h-16 w-16 text-gray-400" fill="none"
                        stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                          stroke-linecap="round" stroke-linejoin="round" stroke-width="1"></path>
                      </svg>
                      <p class="mt-2">Klik gambar untuk melihat preview</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Footer -->
            <div class="flex justify-end space-x-3 border-t border-gray-200 bg-gray-50 px-6 py-4">
              <button
                @click="closePicker()"
                class="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                type="button">
                Batal
              </button>
              <button
                @click="confirmSelection()"
                :disabled="isConfirming"
                :class="isConfirming ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-700'"
                class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                data-raja-picker-select="true"
                type="button">
                <div x-show="isConfirming" class="mr-2">
                  <div class="inline-block h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                </div>
                <span x-text="isConfirming ? 'Memproses...' : 'Pilih'"></span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</x-dynamic-component>

@pushOnce('styles')
  <style>
    .raja-picker-container {
      /* Container styling */
    }

    .raja-picker-preview img {
      transition: all 0.2s ease-in-out;
    }

    .raja-picker-preview img:hover {
      transform: scale(1.02);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .raja-picker-actions button {
      transition: all 0.2s ease-in-out;
    }

    .raja-picker-actions button:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
    }

    .raja-picker-placeholder {
      transition: all 0.2s ease-in-out;
    }

    .raja-picker-placeholder:hover {
      border-color: #3b82f6;
      background-color: #f8fafc;
    }

    /* Modal animations */
    .modal-backdrop {
      backdrop-filter: blur(4px);
    }

    /* Grid hover effects */
    .media-grid-item {
      transition: all 0.2s ease-in-out;
    }

    .media-grid-item:hover {
      transform: scale(1.05);
      z-index: 10;
    }

    /* Loading spinner */
    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }

    .animate-spin {
      animation: spin 1s linear infinite;
    }

    /* File input styling */
    input[type="file"] {
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border: 0;
    }

    /* Preview grid responsive */
    .multiple-preview {
      gap: 1rem;
    }

    @media (max-width: 640px) {
      .multiple-preview {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
      }
    }

    @media (min-width: 641px) and (max-width: 768px) {
      .multiple-preview {
        grid-template-columns: repeat(3, 1fr);
      }
    }

    @media (min-width: 769px) {
      .multiple-preview {
        grid-template-columns: repeat(4, 1fr);
      }
    }

    /* Button group responsive */
    .raja-picker-actions {
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    @media (max-width: 640px) {
      .raja-picker-actions button {
        flex: 1;
        min-width: 0;
      }
    }

    /* Modal responsive */
    @media (max-width: 640px) {
      .modal-content {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
      }
    }

    /* Disabled elements styling when picker is open */
    [data-raja-picker-disabled="true"] {
      cursor: not-allowed !important;
      user-select: none !important;
    }

    /* Form overlay when picker is open */
    .raja-picker-form-overlay {
      position: relative;
    }

    .raja-picker-form-overlay::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      z-index: 10;
      pointer-events: none;
    }
  </style>
@endPushOnce

@pushOnce('scripts')
  <script>
    document.addEventListener('alpine:init', () => {
      Alpine.data('rajaPicker', (config) => ({
        // Configuration
        statePath: config.statePath,
        isMultiple: config.isMultiple,
        collection: config.collection,
        maxFileSize: config.maxFileSize,
        acceptedTypes: config.acceptedTypes,
        enablePicker: config.enablePicker,
        enableUploader: config.enableUploader,
        previewSize: config.previewSize,
        byUser: config.byUser,
        byUserId: config.byUserId,
        convertWebp: config.convertWebp,

        // State - Initialize value directly without method call
        value: (() => {
          const currentValue = config.currentValue;
          const isMultiple = config.isMultiple;

          if (isMultiple) {
            if (Array.isArray(currentValue)) {
              return currentValue;
            } else if (currentValue) {
              return [currentValue];
            } else {
              return [];
            }
          } else {
            if (Array.isArray(currentValue)) {
              return currentValue.length > 0 ? currentValue[0] : null;
            } else {
              return currentValue || null;
            }
          }
        })(),
        selectedMedia: null,
        selectedMediaList: [],
        availableMedia: [],
        availableCollections: [],
        selectedCollection: 'all',
        showPicker: false,
        loadingMedia: false,
        tempSelectedIds: [],
        tempFiles: [], // Store temporary files info
        pagination: null,
        currentPage: 1,
        previewMedia: null,
        isConfirming: false,
        // State tambahan untuk indikator loading
        isOpeningGallery: false,
        isUploading: false,

        // Computed
        get hasValue() {
          if (this.isMultiple) {
            return Array.isArray(this.value) && this.value.length > 0;
          }
          return this.value !== null && this.value !== undefined && this.value !== '';
        },

        // Initialize
        init() {
          this.loadCurrentMedia();

          // Watch for value changes and sync with Livewire
          this.$watch('value', (newValue) => {
            this.syncWithLivewire(newValue);
          });

          // Listen for form submission events
          this.setupFormSubmissionHandlers();

          // Listen for ESC key to close picker
          this.escKeyHandler = (e) => {
            if (e.key === 'Escape' && this.showPicker) {
              this.closePicker();
            }
          };
          document.addEventListener('keydown', this.escKeyHandler);
        },

        // Cleanup when component is destroyed
        destroy() {
          // Cleanup all event handlers
          this.cleanupEventHandlers();
          // Also cleanup form submission prevention
          this.preventFormSubmission(false);
        },

        // Utility method for initializing value (for future use)
        initializeValue(currentValue, isMultiple) {
          if (isMultiple) {
            if (Array.isArray(currentValue)) {
              return currentValue;
            } else if (currentValue) {
              return [currentValue];
            } else {
              return [];
            }
          } else {
            if (Array.isArray(currentValue)) {
              return currentValue.length > 0 ? currentValue[0] : null;
            } else {
              return currentValue || null;
            }
          }
        },

        // Setup form submission handlers
        setupFormSubmissionHandlers() {
          // Find the form element
          const form = this.$el.closest('form');
          console.log('Form found:', form);
          if (form) {
            // Listen for form submit - process temp files before submission
            form.addEventListener('submit', async (e) => {
              console.log('Form submit event triggered, tempFiles length:', this.tempFiles.length);
              if (this.tempFiles.length > 0) {
                console.log('Preventing form submission to process temp files');
                e.preventDefault(); // Prevent form submission
                await this.processTempFilesToFinal();
                // After processing, submit the form
                console.log('Resubmitting form after processing temp files');
                form.submit();
              } else {
                console.log('No temp files, syncing with Livewire');
                this.syncWithLivewire(this.value);
              }
            });
          }

          // Listen for Livewire events
          document.addEventListener('livewire:submit', async () => {
            if (this.tempFiles.length > 0) {
              await this.processTempFilesToFinal();
            }
            this.syncWithLivewire(this.value);
          });

          document.addEventListener('livewire:update', () => {
            this.syncWithLivewire(this.value);
          });
        },

        // Process temporary files to final location
        async processTempFilesToFinal() {
          console.log('processTempFilesToFinal called, tempFiles:', this.tempFiles);
          if (this.tempFiles.length === 0) {
            console.log('No temp files to process');
            return;
          }

          try {
            const tempFilePaths = this.tempFiles.map(f => f.temp_path);
            console.log('Processing temp file paths:', tempFilePaths);

            const formData = new FormData();
            formData.append('tempFiles', JSON.stringify(tempFilePaths));
            formData.append('collection', this.collection);
            formData.append('convertWebp', this.convertWebp.toString());
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

            const response = await fetch('/api/media/upload', {
              method: 'POST',
              body: formData,
              headers: {
                'X-Requested-With': 'XMLHttpRequest',
              }
            });

            if (response.ok) {
              const result = await response.json();

              // Update values with final media URLs
              if (this.isMultiple) {
                if (Array.isArray(result.media)) {
                  const finalUrls = result.media.map(m => this.removeStoragePrefix(m.url));
                  // Replace temp URLs with final URLs
                  this.value = finalUrls;
                  this.selectedMediaList = result.media;
                } else {
                  this.value = [this.removeStoragePrefix(result.media.url)];
                  this.selectedMediaList = [result.media];
                }
              } else {
                if (Array.isArray(result.media)) {
                  this.value = this.removeStoragePrefix(result.media[0].url);
                  this.selectedMedia = result.media[0];
                } else {
                  this.value = this.removeStoragePrefix(result.media.url);
                  this.selectedMedia = result.media;
                }
              }

              // Clear temp files
              this.tempFiles = [];

              console.log('Temporary files processed successfully');
            } else {
              const error = await response.json();
              console.error('Error processing temp files:', error);
            }
          } catch (error) {
            console.error('Error processing temp files:', error);
          }
        },

        // Load current media data
        async loadCurrentMedia() {
          if (!this.hasValue) return;

          try {
            if (this.isMultiple && Array.isArray(this.value)) {
              // Convert URLs to IDs for API call
              const ids = await this.convertUrlsToIds(this.value);
              if (ids.length > 0) {
                const response = await fetch(`/api/media/by-ids?ids=${ids.join(',')}`);
                if (response.ok) {
                  this.selectedMediaList = await response.json();
                }
              }
            } else if (this.value) {
              // Convert URL to ID for API call
              const id = await this.convertUrlToId(this.value);

              if (id) {
                const response = await fetch(`/api/media/${id}`);
                if (response.ok) {
                  this.selectedMedia = await response.json();
                } else {
                  console.error('Failed to fetch media by ID:', id, response.status);
                }
              } else {
                console.warn('Could not convert URL to ID:', this.value);
              }
            }
          } catch (error) {
            console.error('Error loading current media:', error);
          }
        },

        // Open picker modal
        async openPicker() {
          // Tampilkan indikator loading pada tombol Galeri
          this.isOpeningGallery = true;
          this.showPicker = true;
          this.loadingMedia = true;
          this.currentPage = 1;
          this.selectedCollection = 'all';

          // Prevent form submission when picker is open
          this.preventFormSubmission(true);

          // Convert URLs to IDs for picker selection
          this.tempSelectedIds = this.isMultiple ?
            await this.convertUrlsToIds(this.value || []) :
            (this.value ? [await this.convertUrlToId(this.value)] : []);

          try {
            // Load collections first
            await this.loadCollections();

            // Load media with pagination
            await this.loadMediaWithFilter();
          } catch (error) {
            console.error('Error loading picker data:', error);
          } finally {
            this.loadingMedia = false;
            // Sembunyikan indikator loading tombol Galeri
            this.isOpeningGallery = false;
          }
        },

        // Load available collections
        async loadCollections() {
          try {
            const params = new URLSearchParams();
            if (this.byUserId !== null) {
              params.append('user_id', this.byUserId);
            } else if (this.byUser) {
              params.append('by_user', 'true');
            }

            const response = await fetch(`/api/media/collections?${params}`);
            if (response.ok) {
              this.availableCollections = await response.json();
            }
          } catch (error) {
            console.error('Error loading collections:', error);
          }
        },

        // Load media with current filter and pagination
        async loadMediaWithFilter() {
          this.loadingMedia = true;
          try {
            const params = new URLSearchParams({
              page: this.currentPage,
              per_page: 24
            });

            if (this.selectedCollection && this.selectedCollection !== 'all') {
              params.append('collection', this.selectedCollection);
            }

            if (this.byUserId !== null) {
              params.append('user_id', this.byUserId);
            } else if (this.byUser) {
              params.append('by_user', 'true');
            }

            const response = await fetch(`/api/media/all-images?${params}`);
            if (response.ok) {
              const data = await response.json();
              this.availableMedia = data.data || [];
              this.pagination = data.pagination || null;
            }
          } catch (error) {
            console.error('Error loading media:', error);
          } finally {
            this.loadingMedia = false;
          }
        },

        // Load specific page
        async loadPage(page) {
          if (page < 1 || (this.pagination && page > this.pagination.last_page)) {
            return;
          }
          this.currentPage = page;
          await this.loadMediaWithFilter();
        },

        // Get page numbers for pagination
        getPageNumbers() {
          if (!this.pagination) return [];

          const current = this.pagination.current_page;
          const last = this.pagination.last_page;
          const pages = [];

          // Show max 5 page numbers
          let start = Math.max(1, current - 2);
          let end = Math.min(last, current + 2);

          // Adjust if we're near the beginning or end
          if (end - start < 4) {
            if (start === 1) {
              end = Math.min(last, start + 4);
            } else if (end === last) {
              start = Math.max(1, end - 4);
            }
          }

          for (let i = start; i <= end; i++) {
            pages.push(i);
          }

          return pages;
        },

        // Close picker modal
        closePicker() {
          this.showPicker = false;
          this.tempSelectedIds = [];
          this.currentPage = 1;
          this.selectedCollection = 'all';
          this.pagination = null;
          this.previewMedia = null;

          // Re-enable form submission when picker is closed
          this.preventFormSubmission(false);
        },

        // Check if media is selected in picker
        isMediaSelected(mediaId) {
          return this.tempSelectedIds.includes(mediaId);
        },

        // Select media from picker
        selectMediaFromPicker(media) {
          if (this.isMultiple) {
            const index = this.tempSelectedIds.indexOf(media.id);
            if (index > -1) {
              this.tempSelectedIds.splice(index, 1);
            } else {
              this.tempSelectedIds.push(media.id);
            }
          } else {
            this.tempSelectedIds = [media.id];
          }
        },

        // Confirm selection from picker
        async confirmSelection() {
          this.isConfirming = true;

          try {
            if (this.isMultiple) {
            // Convert IDs to URLs for storage
            this.value = await this.convertIdsToUrls(this.tempSelectedIds);
            // Load selected media data
            if (this.tempSelectedIds.length > 0) {
              try {
                const response = await fetch(
                  `/api/media/by-ids?ids=${this.tempSelectedIds.join(',')}`);
                if (response.ok) {
                  this.selectedMediaList = await response.json();
                }
              } catch (error) {
                console.error('Error loading selected media:', error);
              }
            }
          } else {
            // Convert ID to URL for storage
            this.value = this.tempSelectedIds[0] ? await this.convertIdToUrl(this
              .tempSelectedIds[0]) : null;
            // Load selected media data
            if (this.tempSelectedIds[0]) {
              try {
                const response = await fetch(`/api/media/${this.tempSelectedIds[0]}`);
                if (response.ok) {
                  this.selectedMedia = await response.json();
                }
              } catch (error) {
                console.error('Error loading selected media:', error);
              }
            }
          }

          this.closePicker();

          // Force sync with Livewire immediately
          this.syncWithLivewire(this.value);
          } catch (error) {
            console.error('Error confirming selection:', error);
            this.showNotification('Terjadi kesalahan saat memilih gambar', 'error');
            // Re-enable form submission on error
            this.preventFormSubmission(false);
          } finally {
            this.isConfirming = false;
          }
        },

        // Handle file upload to temporary directory
        async handleFileUpload(event) {
          // Tampilkan indikator loading pada tombol Upload
          this.isUploading = true;
          const files = Array.from(event.target.files);
          if (files.length === 0) return;

          // Validate files
          for (const file of files) {
            if (!this.validateFile(file)) {
              return;
            }
          }

          // Upload files to temporary directory
          try {
            const formData = new FormData();

            if (this.isMultiple) {
              files.forEach((file, index) => {
                formData.append(`files[${index}]`, file);
              });
            } else {
              formData.append('file', files[0]);
            }

            formData.append('collection', this.collection);
            formData.append('convertWebp', this.convertWebp.toString());
            formData.append('_token', document.querySelector('meta[name="csrf-token"]')
              .getAttribute('content'));

            const response = await fetch('/api/media/upload-temp', {
              method: 'POST',
              body: formData,
              headers: {
                'X-Requested-With': 'XMLHttpRequest',
              }
            });

            if (response.ok) {
              const result = await response.json();

              if (this.isMultiple) {
                // Handle multiple temp upload result
                if (Array.isArray(result.tempFiles)) {
                  const newTempFiles = result.tempFiles;
                  this.tempFiles = [...(this.tempFiles || []), ...newTempFiles];
                  // Use temp URLs for preview
                  const newUrls = newTempFiles.map(f => this.removeStoragePrefix(f.url));
                  this.value = [...(this.value || []), ...newUrls];
                  // Create mock media objects for display
                  const mockMedia = newTempFiles.map(f => ({
                    id: 'temp_' + Date.now() + '_' + Math.random(),
                    name: f.original_name,
                    url: f.url,
                    temp_path: f.temp_path,
                    collection: f.collection,
                    convert_webp: f.convert_webp
                  }));
                  this.selectedMediaList = [...this.selectedMediaList, ...mockMedia];
                } else {
                  // Single temp file in multiple mode
                  this.tempFiles = [...(this.tempFiles || []), result.tempFile];
                  this.value = [...(this.value || []), this.removeStoragePrefix(result.tempFile.url)];
                  const mockMedia = {
                    id: 'temp_' + Date.now() + '_' + Math.random(),
                    name: result.tempFile.original_name,
                    url: result.tempFile.url,
                    temp_path: result.tempFile.temp_path,
                    collection: result.tempFile.collection,
                    convert_webp: result.tempFile.convert_webp
                  };
                  this.selectedMediaList = [...this.selectedMediaList, mockMedia];
                }
              } else {
                // Handle single temp upload result
                if (Array.isArray(result.tempFiles)) {
                  this.tempFiles = result.tempFiles;
                  this.value = this.removeStoragePrefix(result.tempFiles[0].url);
                  this.selectedMedia = {
                    id: 'temp_' + Date.now() + '_' + Math.random(),
                    name: result.tempFiles[0].original_name,
                    url: result.tempFiles[0].url,
                    temp_path: result.tempFiles[0].temp_path,
                    collection: result.tempFiles[0].collection,
                    convert_webp: result.tempFiles[0].convert_webp
                  };
                } else {
                  this.tempFiles = [result.tempFile];
                  this.value = this.removeStoragePrefix(result.tempFile.url);
                  this.selectedMedia = {
                    id: 'temp_' + Date.now() + '_' + Math.random(),
                    name: result.tempFile.original_name,
                    url: result.tempFile.url,
                    temp_path: result.tempFile.temp_path,
                    collection: result.tempFile.collection,
                    convert_webp: result.tempFile.convert_webp
                  };
                }
              }

              // Force sync with Livewire immediately
              this.syncWithLivewire(this.value);
              console.log('Temp files after upload:', this.tempFiles);
              this.showNotification('Gambar berhasil diupload ke temporary!', 'success');
            } else {
              const error = await response.json();
              this.showNotification(error.message || 'Gagal mengupload gambar', 'error');
            }
          } catch (error) {
            console.error('Upload error:', error);
            this.showNotification('Terjadi kesalahan saat mengupload', 'error');
          }

          // Reset file input
          event.target.value = '';
          // Sembunyikan indikator loading tombol Upload
          this.isUploading = false;
        },

        // Validate file
        validateFile(file) {
          // Check file type
          if (!this.acceptedTypes.split(',').includes(file.type)) {
            this.showNotification(`Tipe file ${file.type} tidak didukung`, 'error');
            return false;
          }

          // Check file size
          const maxSizeBytes = this.maxFileSize * 1024 * 1024;
          if (file.size > maxSizeBytes) {
            this.showNotification(
              `Ukuran file terlalu besar. Maksimal ${this.maxFileSize}MB`, 'error');
            return false;
          }

          return true;
        },

        // Remove media
        removeMedia() {
          this.value = this.isMultiple ? [] : null;
          this.selectedMedia = null;
          this.selectedMediaList = [];
          this.notifyChange();
        },

        // Remove media at index (for multiple)
        removeMediaAtIndex(index) {
          if (this.isMultiple && Array.isArray(this.value)) {
            this.value.splice(index, 1);
            this.selectedMediaList.splice(index, 1);
            this.notifyChange();
          }
        },

        // Clear selection
        clearSelection() {
          this.removeMedia();
        },

        // Format file size
        formatFileSize(bytes) {
          if (bytes === 0) return '0 Bytes';
          const k = 1024;
          const sizes = ['Bytes', 'KB', 'MB', 'GB'];
          const i = Math.floor(Math.log(bytes) / Math.log(k));
          return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },

        // Sync with Livewire
        syncWithLivewire(value) {
          // Prepare value for Livewire
          let livewireValue;

          if (this.isMultiple) {
            // For multiple selection, send as array
            livewireValue = Array.isArray(value) ? value : (value ? [value] : []);
          } else {
            // For single selection, send as single value
            livewireValue = Array.isArray(value) ? (value.length > 0 ? value[0] : null) :
              value;
          }

          // Update the hidden input value
          const hiddenInput = this.$el.querySelector(`input[name="${this.statePath}"]`);
          if (hiddenInput) {
            // Set the value directly (Livewire will handle JSON encoding if needed)
            hiddenInput.value = livewireValue !== null && livewireValue !== undefined ?
              (Array.isArray(livewireValue) ? JSON.stringify(livewireValue) :
              livewireValue) : '';

            // Trigger input event for Livewire
            hiddenInput.dispatchEvent(new Event('input', {
              bubbles: true
            }));
            hiddenInput.dispatchEvent(new Event('change', {
              bubbles: true
            }));
          }

          // Also dispatch Alpine event
          this.$dispatch('input', livewireValue);
        },

        // Notify change to Livewire
        notifyChange() {
          this.$nextTick(() => {
            this.syncWithLivewire(this.value);
          });
        },

        // Show notification
        showNotification(message, type = 'info') {
          // Use Filament's notification system if available
          if (window.$wire && window.$wire.notify) {
            window.$wire.notify(message, type);
          }
        },

        // Prevent form submission when picker is open
        preventFormSubmission(prevent = true) {
          const form = this.$el.closest('form');
          if (!form) return;

          if (prevent) {
            // Add visual overlay to form
            form.classList.add('raja-picker-form-overlay');

            // Add event listeners to prevent form submission
            this.formSubmitHandler = (e) => {
              // Allow only the "Pilih" button to submit
              const target = e.target;
              const isSelectButton = this.isSelectButton(target);

              if (!isSelectButton) {
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
                return false;
              }
            };

            this.clickHandler = (e) => {
              // Prevent clicks on all elements except "Pilih" button and picker content when picker is open
              if (this.showPicker) {
                const target = e.target;
                const isSelectButton = this.isSelectButton(target);
                const isPickerContent = target.closest('.raja-picker-container');

                if (!isSelectButton && !isPickerContent) {
                  e.preventDefault();
                  e.stopPropagation();
                  e.stopImmediatePropagation();
                  return false;
                }
              }
            };

            this.keydownHandler = (e) => {
              // Prevent Enter key submission when picker is open
              if (this.showPicker && e.key === 'Enter') {
                const target = e.target;
                const isSelectButton = this.isSelectButton(target);

                if (!isSelectButton) {
                  e.preventDefault();
                  e.stopPropagation();
                  e.stopImmediatePropagation();
                  return false;
                }
              }
            };

            // Add event listeners with high priority
            form.addEventListener('submit', this.formSubmitHandler, true);
            document.addEventListener('click', this.clickHandler, true);
            document.addEventListener('keydown', this.keydownHandler, true);

            // Disable all buttons except the select button
            const buttons = form.querySelectorAll('button:not([data-raja-picker-select="true"])');
            buttons.forEach(button => {
              if (!button.closest('.raja-picker-container')) {
                button.disabled = true;
                button.style.pointerEvents = 'none';
                button.style.opacity = '0.5';
                button.setAttribute('data-raja-picker-disabled', 'true');
              }
            });

            // Disable all links outside picker
            const links = form.querySelectorAll('a');
            links.forEach(link => {
              if (!link.closest('.raja-picker-container')) {
                link.style.pointerEvents = 'none';
                link.style.opacity = '0.5';
                link.setAttribute('data-raja-picker-disabled', 'true');
              }
            });

            // Disable all inputs outside picker
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
              if (!input.closest('.raja-picker-container')) {
                input.disabled = true;
                input.setAttribute('data-raja-picker-disabled', 'true');
              }
            });

          } else {
            // Remove visual overlay from form
            form.classList.remove('raja-picker-form-overlay');

            // Remove event listeners
            if (this.formSubmitHandler) {
              form.removeEventListener('submit', this.formSubmitHandler, true);
            }
            if (this.clickHandler) {
              document.removeEventListener('click', this.clickHandler, true);
            }
            if (this.keydownHandler) {
              document.removeEventListener('keydown', this.keydownHandler, true);
            }

            // Re-enable all disabled elements
            const disabledElements = form.querySelectorAll('[data-raja-picker-disabled="true"]');
            disabledElements.forEach(element => {
              element.disabled = false;
              element.style.pointerEvents = '';
              element.style.opacity = '';
              element.removeAttribute('data-raja-picker-disabled');
            });
          }
        },

        // Helper method to check if target is the select button
        isSelectButton(target) {
          if (!target) return false;

          // Check if target itself is the select button
          if (target.hasAttribute && target.hasAttribute('data-raja-picker-select')) {
            return true;
          }

          // Check if target is inside the select button
          if (target.closest) {
            const selectButton = target.closest('[data-raja-picker-select="true"]');
            if (selectButton) {
              return true;
            }

            // Check by text content as fallback
            const button = target.closest('button');
            if (button && button.textContent && button.textContent.includes('Pilih')) {
              return true;
            }
          }

          // Additional fallback for older browsers
          let element = target;
          while (element && element !== document) {
            if (element.tagName === 'BUTTON' &&
                element.getAttribute &&
                element.getAttribute('data-raja-picker-select') === 'true') {
              return true;
            }
            if (element.tagName === 'BUTTON' &&
                element.textContent &&
                element.textContent.includes('Pilih')) {
              return true;
            }
            element = element.parentElement;
          }

          return false;
        },

        // Cleanup all event handlers
        cleanupEventHandlers() {
          const form = this.$el.closest('form');
          if (form) {
            // Remove all our event handlers
            if (this.formSubmitHandler) {
              form.removeEventListener('submit', this.formSubmitHandler, true);
              this.formSubmitHandler = null;
            }
            if (this.clickHandler) {
              document.removeEventListener('click', this.clickHandler, true);
              this.clickHandler = null;
            }
            if (this.keydownHandler) {
              document.removeEventListener('keydown', this.keydownHandler, true);
              this.keydownHandler = null;
            }
            if (this.escKeyHandler) {
              document.removeEventListener('keydown', this.escKeyHandler);
              this.escKeyHandler = null;
            }
          }
        },

        // Convert URL to ID
        async convertUrlToId(url) {
          if (!url || typeof url !== 'string') return null;

          // If already an ID (numeric), return as is
          if (!isNaN(url)) return parseInt(url);

          // If not a URL, return null
          if (!url.includes('/')) return null;

          try {
            // Extract filename from URL
            const filename = url.split('/').pop();

            // First try to find in current collection
            let response = await fetch(`/api/media/collection/${this.collection}`);
            if (response.ok) {
              const mediaList = await response.json();
              const media = mediaList.find(m => m.file_name === filename);
              if (media) {
                return media.id;
              }
            }

            // If not found in current collection, try to find in all images
            response = await fetch('/api/media/all-images?per_page=1000');
            if (response.ok) {
              const data = await response.json();
              const allMedia = data.data || [];

              // Try to match by filename
              let media = allMedia.find(m => m.file_name === filename);
              if (media) {
                return media.id;
              }

              // Try to match by URL path (for cases where URL format is different)
              media = allMedia.find(m => {
                if (m.url && url) {
                  // Compare the last part of both URLs
                  const mediaFilename = m.url.split('/').pop();
                  const urlFilename = url.split('/').pop();
                  return mediaFilename === urlFilename;
                }
                return false;
              });
              if (media) {
                return media.id;
              }

              // Try to match by partial URL (for legacy data)
              media = allMedia.find(m => {
                if (m.url && url) {
                  // Check if either URL contains the other's filename
                  const mediaFilename = m.url.split('/').pop().split('.')[0]; // without extension
                  const urlFilename = url.split('/').pop().split('.')[0]; // without extension
                  return mediaFilename.includes(urlFilename) || urlFilename.includes(mediaFilename);
                }
                return false;
              });
              if (media) {
                return media.id;
              }
            }
          } catch (error) {
            console.error('Error converting URL to ID:', error);
          }

          return null;
        },

        // Convert URLs to IDs
        async convertUrlsToIds(urls) {
          if (!Array.isArray(urls)) return [];

          const ids = [];
          for (const url of urls) {
            const id = await this.convertUrlToId(url);
            if (id) ids.push(id);
          }
          return ids;
        },

        // Remove /storage/ prefix and leading slash from URL for storage
        removeStoragePrefix(url) {
          if (typeof url === 'string' && url.startsWith('/storage/')) {
            let cleanUrl = url.substring(8); // Remove '/storage' (8 characters)
            // Remove leading slash for cleaner storage
            if (cleanUrl.startsWith('/')) {
              cleanUrl = cleanUrl.substring(1);
            }
            return cleanUrl;
          }
          // Remove leading slash if present
          if (typeof url === 'string' && url.startsWith('/') && !url.startsWith('http')) {
            return url.substring(1);
          }
          return url;
        },

        // Add /storage/ prefix to URL for display
        addStoragePrefix(url) {
          if (!url || typeof url !== 'string') return url;

          // Don't add prefix if URL already has it or is a full URL
          if (url.startsWith('/storage/') || url.startsWith('http')) {
            return url;
          }

          // Add /storage/ prefix - handle both with and without leading slash
          if (url.startsWith('/')) {
            return '/storage' + url;
          }

          return '/storage/' + url;
        },

        // Convert ID to URL (remove /storage/ prefix for storage)
        async convertIdToUrl(id) {
          if (!id) return null;

          // If already a URL, clean it for storage
          if (typeof id === 'string' && (id.includes('http') || id.includes('/'))) {
            return this.removeStoragePrefix(id);
          }

          try {
            const response = await fetch(`/api/media/${id}`);
            if (response.ok) {
              const media = await response.json();
              // Remove /storage/ prefix for storage
              return this.removeStoragePrefix(media.url);
            }
          } catch (error) {
            console.error('Error converting ID to URL:', error);
          }

          return null;
        },

        // Convert IDs to URLs (remove /storage/ prefix for storage)
        async convertIdsToUrls(ids) {
          if (!Array.isArray(ids)) return [];

          const urls = [];
          for (const id of ids) {
            const url = await this.convertIdToUrl(id);
            if (url) urls.push(url);
          }
          return urls;
        }
      }));
    });
  </script>
@endPushOnce
